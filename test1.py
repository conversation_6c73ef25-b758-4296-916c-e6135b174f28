# Copyright (c) OpenMMLab. All rights reserved.
import os
import sys
import random
import time
from pathlib import Path
from argparse import ArgumentParser

import cv2
import mmcv
import numpy as np
import torch
from PIL import Image
from typing import List, Tuple

# Custom module imports
sys.path.append(str(Path(__file__).resolve().parents[3]))
sys.path.append('/home/<USER>/panpan/mm_detection/test_carm')

from projects.easydeploy.model import ORTWrapper  # noqa: E402
from utils import (  # noqa: E402
    bbox_postprocess, 
    preprocess, 
    visualize_detections,
    reg_max2bbox,
    resize_and_pad
)
from decode import (  # noqa: E402
    predict_by_feat,
    _bbox_post_process,
    get_single_pred,
    
)
from easydict import EasyDict
from pixel_to_physical_py.src.detection_processor import DetectionProcessor, DetectionResult, BBox
from pixel_to_physical_py.src.camera_coordinate_converter import CameraCoordinateConverter, CameraCoordinate

# Configuration and Constants
COLORS = [[random.randint(0, 255) for _ in range(3)] for _ in range(1000)]
CLASS_NAMES = ['bin', 'cloth', 'rug', 'shoe', 'wire', 'rail', 'wheel']
def transform_point(point, scale_factor, pad_param):
    x, y, conf = point
    top, bottom, left, right = pad_param
    x_actual = (x - left) / scale_factor
    y_actual = (y - top)  / scale_factor
    return (x_actual, y_actual, conf)

def parse_args():
    parser = ArgumentParser()
    parser.add_argument('--camera-id', type=int, default=0)
    parser.add_argument('--frame-size', nargs=2, type=int, default=[1280, 720])
    parser.add_argument('--config', default="yolov8s_old7cls_640.py")
    parser.add_argument('--checkpoint', '-c', default="yolov8v6_only.onnx")
    parser.add_argument('--line_config', default="line_in_five_head.py")
    parser.add_argument('--line_checkpoint', '-l', default="yolov8v13_only.onnx")
    parser.add_argument('--out-dir', default='camera_output')
    parser.add_argument('--device', default='cuda:0')
    parser.add_argument('--fps-display', action='store_true')
    parser.add_argument('--outlier-mode', choices=['filter', 'correct'], default='correct')
    parser.add_argument('--camera-outlier-mode', choices=['filter', 'correct'], default='correct')
    parser.add_argument('--window-size', type=int, default=20)
    parser.add_argument('--threshold-ratio', type=float, default=1)
    parser.add_argument('--prominence', type=float, default=10)
    parser.add_argument('--neighbor-window', type=int, default=2)
    return parser.parse_args()

def load_model(checkpoint_path, device):
    if checkpoint_path.endswith('.onnx'):
        model = ORTWrapper(checkpoint_path, device)
    model.to(device)
    return model

def build_preprocessing_pipeline():
    return {
        'pipeline': resize_and_pad,
        'preprocessor': preprocess(),
    }

def preprocess_image(rgb_img, pipeline, preprocessor, device):
    processed = pipeline(rgb_img)
    data, scale, pad_param = processed
    scale = 1.0/scale
    data = preprocessor(data).to(device)
    samples = {"scale": scale, "pad_param": pad_param}
    return data, samples

def process_detection_results(feats, device, test_cfg, original_shape, scale_factor, pad_param):
    bbox_preds, cls_scores = [], []
    for feat in feats:
        bbox_pred, cls_score = torch.split(feat, [64, 7], dim=1)
        bbox_preds.append(bbox_pred)
        cls_scores.append(cls_score)
    if bbox_preds[0].shape[1] == 64:
        proj = torch.arange(16, dtype=torch.float).to(device)
        bbox_preds = [reg_max2bbox(bbox, proj) for bbox in bbox_preds]
    batch_img_metas = [{
        'ori_shape': original_shape,
        'pad_shape': (640, 640, 3),
        'scale_factor': scale_factor,
        'pad_param': pad_param
    }]
    return predict_by_feat(cls_scores, bbox_preds, objectnesses=None,
                           batch_img_metas=batch_img_metas, cfg=test_cfg,
                           post_processing=_bbox_post_process)

def _preprocess_image(img: np.ndarray, calibration_mapx_path, calibration_mapy_path) -> np.ndarray:
    calibration_mapx = np.fromfile(calibration_mapx_path, dtype=np.float32).reshape(img.shape[0], img.shape[1])
    calibration_mapy = np.fromfile(calibration_mapy_path, dtype=np.float32).reshape(img.shape[0], img.shape[1])
    processed_img = cv2.remap(img, calibration_mapx, calibration_mapy, cv2.INTER_LINEAR)
    print("使用标定映射表处理图像")
    return processed_img

def _initialize_physical_processor() -> DetectionProcessor:
    return DetectionProcessor()

def _initialize_camera_converter() -> CameraCoordinateConverter:
    camera_config_path = "pixel_to_physical_py/config/calib_intrix_new.yaml"
    return CameraCoordinateConverter(camera_config_path)

def _convert_points_to_detection_results(points_data: List[Tuple], point_size: int = 10) -> List[DetectionResult]:
    detection_results = []
    for i, (x_actual, y_actual, conf) in enumerate(points_data):
        bbox_obj = BBox(x1=int(x_actual), y1=int(y_actual), x2=int(x_actual), y2=int(y_actual))
        det_result = DetectionResult(bbox=bbox_obj, label=f"point_{i}", confidence=float(conf))
        detection_results.append(det_result)
    return detection_results


def main():
    args = parse_args()
    # cap = cv2.VideoCapture(args.camera_id)
    # cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
    # cap.set(cv2.CAP_PROP_FPS, 30)
    # cap.set(cv2.CAP_PROP_FRAME_WIDTH, args.frame_size[0])
    # cap.set(cv2.CAP_PROP_FRAME_HEIGHT, args.frame_size[1])
    # cv2.namedWindow("监控视角 - 检测结果", cv2.WINDOW_NORMAL)
    # cv2.resizeWindow("监控视角 - 检测结果", 1280, 720)

    calibration_mapx = "pixel_to_physical_py/config/mapx"
    calibration_mapy = "pixel_to_physical_py/config/mapy"
    physical_processor = _initialize_physical_processor()
    camera_converter = _initialize_camera_converter()

    model = load_model(args.checkpoint, args.device)
    line_model = load_model(args.line_checkpoint, args.device)
    main_pp = build_preprocessing_pipeline()
    test_cfg = EasyDict(
        max_per_img=300,
        multi_label=True,
        nms=dict(iou_threshold=0.7, type='nms'),
        nms_pre=30000,
        score_thr=0.001)

    frame_count, start_time = 0, time.time()
    print("开始实时检测，按ESC键退出...")

    frame = cv2.imread("/home/<USER>/panpan/code/Calib/test_carm/41_real.png")
    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    original_shape = rgb_frame.shape
    # rgb_frame = _preprocess_image(rgb_frame, calibration_mapx, calibration_mapy)
    main_data, main_samples = preprocess_image(rgb_frame, main_pp['pipeline'], main_pp['preprocessor'], args.device)
    main_result = model(main_data)
    main_results = process_detection_results(main_result, args.device, test_cfg, original_shape,
                                                main_samples.get('scale', 1), main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32)))

    line_data, _ = preprocess_image(rgb_frame, main_pp['pipeline'], main_pp['preprocessor'], args.device)
    line_result = line_model(line_data)
    line_preds, line_scores = torch.split(line_result[0], [2, 1], dim=1)
    _, line_points, _ = get_single_pred(line_scores, line_preds, (640, 640))

    scale_factor = main_samples.get('scale', 1)
    pad_param = main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32))
    transformed_points = [transform_point(p, scale_factor, pad_param) for p in line_points if p[2] > 0.5]

    # 先绘制原始检测点（红色小圆点）
    for pt in transformed_points:
        cv2.circle(rgb_frame, (int(pt[0]), int(pt[1])), 5, (0, 0, 255), -1)

    distance_table_path = "pixel_to_physical_py/config/distance_table"
    detection_results = _convert_points_to_detection_results(transformed_points)
    valid_results = []

    for i, det_result in enumerate(detection_results):
        physical_processor.process_detection_result(det_result, distance_table_path)
        if det_result.physical_distance > 0:
            valid_results.append((det_result, transformed_points[i]))
    
    depth_values = np.load('/home/<USER>/panpan/code/Calib/test_carm/41_real_depth.npy')
    min_entry = min(valid_results, key=lambda x: x[0].physical_distance)
    min_distance = min_entry[0].physical_distance
    min_point_coords = min_entry[1]

    print(f"最小距离: {min_distance}")
    print(f"对应坐标: {min_point_coords}")
    
    scale = min_distance / (depth_values[int(min_point_coords[1]), int(min_point_coords[0])])
    print(scale)
    print(depth_values[int(min_point_coords[1]), int(min_point_coords[0])])
    for i, (det_result, point_coords) in enumerate(valid_results):
        point_x, point_y = int(point_coords[0]), int(point_coords[1])
        depth_value = depth_values[point_y, point_x]
        det_result.physical_distance = depth_value
        print(f"  点 {i+1}: 位置({point_x}, {point_y}), 深度={depth_value:.1f}cm")

    # 可视化距离值
    print(f"\n=== 可视化 {len(valid_results)} 个检测点的距离值 ===")
    for i, (det_result, point_coords) in enumerate(valid_results):
        point_x, point_y = int(point_coords[0]), int(point_coords[1])
        distance_value = det_result.physical_distance
        

        # 在圆圈内显示距离值
        point_x, point_y = int(point_coords[0]), int(point_coords[1])
        distance_value = det_result.physical_distance  # 取整数，更简洁
        distance_text = f"{distance_value}"

        # 根据距离选择颜色
        if distance_value < 30:
            circle_color = (0, 255, 0)  # 绿色 - 近距离
        elif distance_value < 60:
            circle_color = (255, 255, 0)  # 黄色 - 中距离
        else:
            circle_color = (255, 0, 0)  # 红色 - 远距离

        border_color = (255, 255, 255)  # 白色边框

        # 圆圈和字体设置
        circle_radius = 8
        font_scale = 0.3

        # 绘制填充的圆圈作为背景
        cv2.circle(rgb_frame, (point_x, point_y), circle_radius, circle_color, -1)

        # 绘制圆圈边框（修正过的点用不同颜色边框）
        cv2.circle(rgb_frame, (point_x, point_y), circle_radius, border_color, 2)

        # 计算文字位置（居中）
        text_size = cv2.getTextSize(distance_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, 1)[0]
        text_x = point_x - text_size[0] // 2
        text_y = point_y + text_size[1] // 2

        # 在圆圈内绘制距离数字（黑色）
        cv2.putText(rgb_frame, distance_text, (text_x, text_y),
                    cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), 1)

        print(f"  点 {i+1}: 位置({point_x}, {point_y}), 距离={distance_value:.2f}cm")
    display_img = cv2.cvtColor(rgb_frame, cv2.COLOR_RGB2BGR)
    cv2.imwrite(f"output/{frame_count}.png", display_img)

    

if __name__ == '__main__':
    main()

