#!/usr/bin/env python3
"""
测试 create_boxes_from_points 函数的height过滤功能
"""

class MockDetectionResult:
    """模拟检测结果类"""
    def __init__(self, confidence):
        self.confidence = confidence

def create_boxes_from_points(valid_results, window_size=3, max_height_threshold=50):
    """
    从点创建矩形框，并过滤掉height较大的框以及框内的点
    
    Args:
        valid_results: 包含(det_result, point_coords)元组的列表
        window_size: 窗口大小，默认为3
        max_height_threshold: 最大height阈值，超过此值的框将被过滤掉，默认为50
    
    Returns:
        tuple: (filtered_boxes, filtered_valid_results)
            - filtered_boxes: 过滤后的矩形框列表
            - filtered_valid_results: 过滤后的有效结果列表（删除了height较大框内的点）
    """
    boxes = []
    filtered_valid_results = []
    valid_point_indices = set()  # 记录保留的点的索引
    
    # 遍历点，每次跳过window_size个点
    for i in range(0, len(valid_results) - window_size + 1, window_size):
        # 获取当前窗口的点
        window_points = valid_results[i:i+window_size]

        min_x = min(p[1][0] for p in window_points)
        max_x = max(p[1][0] for p in window_points)
        
        # 确定矩形框的y坐标（取最小y作为底部，最大y+固定高度作为顶部）
        min_y = min(p[1][1] for p in window_points)
        max_y = max(p[1][1] for p in window_points)
        height = max_y - min_y 

        print(f"height: {height}, threshold: {max_height_threshold}")
        
        # 只保留height小于等于阈值的框
        if height <= max_height_threshold:
            # 创建矩形框 (x1, y1, x2, y2, confidence)
            box = (min_x, min_y, max_x, max_y, window_points[0][0].confidence)
            boxes.append(box)
            
            # 记录这个窗口内的点索引，这些点将被保留
            for j in range(i, i + window_size):
                if j < len(valid_results):
                    valid_point_indices.add(j)
        else:
            print(f"过滤掉height={height}的框，超过阈值{max_height_threshold}")
    
    # 根据保留的点索引构建过滤后的valid_results
    filtered_valid_results = [valid_results[i] for i in range(len(valid_results)) if i in valid_point_indices]
    
    print(f"原始点数: {len(valid_results)}, 过滤后点数: {len(filtered_valid_results)}")
    print(f"原始框数: {(len(valid_results) - window_size + 1) // window_size if len(valid_results) >= window_size else 0}, 过滤后框数: {len(boxes)}")
    
    return boxes, filtered_valid_results

def test_box_filtering():
    """测试函数"""
    print("=== 测试 create_boxes_from_points 函数的height过滤功能 ===\n")
    
    # 创建测试数据
    # 格式: (det_result, point_coords)，其中point_coords是(x, y, confidence)
    test_data = [
        # 第一组点 - height较小，应该保留
        (MockDetectionResult(0.9), (100, 100, 0.9)),
        (MockDetectionResult(0.8), (110, 105, 0.8)),
        (MockDetectionResult(0.85), (120, 110, 0.85)),
        
        # 第二组点 - height较大，应该被过滤
        (MockDetectionResult(0.7), (200, 50, 0.7)),
        (MockDetectionResult(0.75), (210, 150, 0.75)),  # height = 150-50 = 100 > 50
        (MockDetectionResult(0.8), (220, 120, 0.8)),
        
        # 第三组点 - height较小，应该保留
        (MockDetectionResult(0.9), (300, 200, 0.9)),
        (MockDetectionResult(0.85), (310, 220, 0.85)),
        (MockDetectionResult(0.88), (320, 210, 0.88)),
    ]
    
    print("原始测试数据:")
    for i, (det_result, point_coords) in enumerate(test_data):
        print(f"  点 {i}: 位置({point_coords[0]}, {point_coords[1]}), confidence={point_coords[2]}")
    
    print(f"\n窗口大小: 3")
    print(f"Height阈值: 50")
    print("\n开始处理...")
    
    # 调用函数
    boxes, filtered_results = create_boxes_from_points(test_data, window_size=3, max_height_threshold=50)
    
    print(f"\n=== 结果 ===")
    print(f"过滤后的矩形框 ({len(boxes)} 个):")
    for i, box in enumerate(boxes):
        x1, y1, x2, y2, conf = box
        height = y2 - y1
        width = x2 - x1
        print(f"  框 {i}: ({x1}, {y1}) - ({x2}, {y2}), width={width}, height={height}, conf={conf}")
    
    print(f"\n过滤后的点 ({len(filtered_results)} 个):")
    for i, (det_result, point_coords) in enumerate(filtered_results):
        print(f"  点 {i}: 位置({point_coords[0]}, {point_coords[1]}), confidence={point_coords[2]}")

if __name__ == "__main__":
    test_box_filtering()
