# Copyright (c) OpenMMLab. All rights reserved.
import os
import sys
import random
import time
from pathlib import Path
from argparse import ArgumentParser

import cv2
import mmcv
import numpy as np
import torch
from PIL import Image
from typing import List, Tuple

# Custom module imports
sys.path.append(str(Path(__file__).resolve().parents[3]))
sys.path.append('/home/<USER>/panpan/mm_detection/test_carm')

from projects.easydeploy.model import ORTWrapper  # noqa: E402
from utils import (  # noqa: E402
    bbox_postprocess, 
    preprocess, 
    visualize_detections,
    reg_max2bbox,
    resize_and_pad
)
from decode import (  # noqa: E402
    predict_by_feat,
    _bbox_post_process,
    get_single_pred,
    
)
from easydict import EasyDict
from pixel_to_physical_py.src.detection_processor import DetectionProcessor, DetectionResult, BBox
from pixel_to_physical_py.src.camera_coordinate_converter import CameraCoordinateConverter, CameraCoordinate

# Configuration and Constants
COLORS = [[random.randint(0, 255) for _ in range(3)] for _ in range(1000)]
CLASS_NAMES = ['bin', 'cloth', 'rug', 'shoe', 'wire', 'rail', 'wheel']
def transform_point(point, scale_factor, pad_param):
    x, y, conf = point
    top, bottom, left, right = pad_param
    x_actual = (x - left) / scale_factor
    y_actual = (y - top)  / scale_factor
    return (x_actual, y_actual, conf)

def parse_args():
    parser = ArgumentParser()
    parser.add_argument('--camera-id', type=int, default=0)
    parser.add_argument('--frame-size', nargs=2, type=int, default=[1280, 720])
    parser.add_argument('--config', default="yolov8s_old7cls_640.py")
    parser.add_argument('--checkpoint', '-c', default="yolov8v6_only.onnx")
    parser.add_argument('--line_config', default="line_in_five_head.py")
    parser.add_argument('--line_checkpoint', '-l', default="yolov8v13_only.onnx")
    parser.add_argument('--out-dir', default='camera_output')
    parser.add_argument('--device', default='cuda:0')
    parser.add_argument('--fps-display', action='store_true')
    parser.add_argument('--outlier-mode', choices=['filter', 'correct'], default='correct')
    parser.add_argument('--camera-outlier-mode', choices=['filter', 'correct'], default='correct')
    parser.add_argument('--window-size', type=int, default=20)
    parser.add_argument('--threshold-ratio', type=float, default=1)
    parser.add_argument('--prominence', type=float, default=10)
    parser.add_argument('--neighbor-window', type=int, default=2)
    return parser.parse_args()

def load_model(checkpoint_path, device):
    if checkpoint_path.endswith('.onnx'):
        model = ORTWrapper(checkpoint_path, device)
    model.to(device)
    return model

def build_preprocessing_pipeline():
    return {
        'pipeline': resize_and_pad,
        'preprocessor': preprocess(),
    }

def preprocess_image(rgb_img, pipeline, preprocessor, device):
    processed = pipeline(rgb_img)
    data, scale, pad_param = processed
    scale = 1.0/scale
    data = preprocessor(data).to(device)
    samples = {"scale": scale, "pad_param": pad_param}
    return data, samples

def process_detection_results(feats, device, test_cfg, original_shape, scale_factor, pad_param):
    bbox_preds, cls_scores = [], []
    for feat in feats:
        bbox_pred, cls_score = torch.split(feat, [64, 7], dim=1)
        bbox_preds.append(bbox_pred)
        cls_scores.append(cls_score)
    if bbox_preds[0].shape[1] == 64:
        proj = torch.arange(16, dtype=torch.float).to(device)
        bbox_preds = [reg_max2bbox(bbox, proj) for bbox in bbox_preds]
    batch_img_metas = [{
        'ori_shape': original_shape,
        'pad_shape': (640, 640, 3),
        'scale_factor': scale_factor,
        'pad_param': pad_param
    }]
    return predict_by_feat(cls_scores, bbox_preds, objectnesses=None,
                           batch_img_metas=batch_img_metas, cfg=test_cfg,
                           post_processing=_bbox_post_process)

def _preprocess_image(img: np.ndarray, calibration_mapx_path, calibration_mapy_path) -> np.ndarray:
    calibration_mapx = np.fromfile(calibration_mapx_path, dtype=np.float32).reshape(img.shape[0], img.shape[1])
    calibration_mapy = np.fromfile(calibration_mapy_path, dtype=np.float32).reshape(img.shape[0], img.shape[1])
    processed_img = cv2.remap(img, calibration_mapx, calibration_mapy, cv2.INTER_LINEAR)
    print("使用标定映射表处理图像")
    return processed_img

def _initialize_physical_processor() -> DetectionProcessor:
    return DetectionProcessor()

def _initialize_camera_converter() -> CameraCoordinateConverter:
    camera_config_path = "pixel_to_physical_py/config/calib_intrix_new.yaml"
    return CameraCoordinateConverter(camera_config_path)

def _convert_points_to_detection_results(points_data: List[Tuple], point_size: int = 10) -> List[DetectionResult]:
    detection_results = []
    for i, (x_actual, y_actual, conf) in enumerate(points_data):
        bbox_obj = BBox(x1=int(x_actual), y1=int(y_actual), x2=int(x_actual), y2=int(y_actual))
        det_result = DetectionResult(bbox=bbox_obj, label=f"point_{i}", confidence=float(conf))
        detection_results.append(det_result)
    return detection_results

def _convert_boxes_to_detection_results(boxes_data: List[Tuple]) -> List[DetectionResult]:
    detection_results = []
    for i, (x1, y1, x2, y2, conf) in enumerate(boxes_data):
        bbox_obj = BBox(x1=int(x1), y1=int(y1), x2=int(x2), y2=int(y2))
        det_result = DetectionResult(bbox=bbox_obj, label=f"wire_{i}", confidence=float(conf))
        detection_results.append(det_result)
    return detection_results

import math
def sort_points_by_line(points):
    """
    按线条方向重新排序像素坐标点，支持多条不连续的线
    :param points: 包含(x,y)坐标的列表，格式可以是元组或列表
    :return: 按线条方向排序后的坐标列表
    """
    if not points:
        return []

    # 转换为(x,y,z)元组列表（如果输入是其他格式）
    coords = [(float(p[0]), float(p[1]), float(p[2])) if isinstance(p, (list, tuple)) else p for p in points]

    # 分离多条线
    lines = separate_lines(coords)

    # 对每条线分别排序
    sorted_lines = []
    for line in lines:
        sorted_line = sort_single_line(line)
        sorted_lines.append(sorted_line)

    # 将所有排序后的线合并，按线的起始点位置排序
    sorted_lines.sort(key=lambda line: (line[0][1], line[0][0]))  # 按y坐标，然后x坐标排序

    # 合并所有线的点
    result = []
    for line in sorted_lines:
        result.extend(line)

    return result

def separate_lines(coords, max_distance_threshold=50):
    """
    将点分离成多条不连续的线
    :param coords: 坐标点列表
    :param max_distance_threshold: 判断点是否属于同一条线的最大距离阈值
    :return: 线的列表，每条线是一个点的列表
    """
    if not coords:
        return []

    lines = []
    remaining_points = coords.copy()

    while remaining_points:
        # 开始新的一条线
        current_line = [remaining_points.pop(0)]

        # 不断添加与当前线相近的点
        added_point = True
        while added_point and remaining_points:
            added_point = False
            for i, point in enumerate(remaining_points):
                # 检查这个点是否与当前线的任何端点足够近
                min_dist_to_line = min(
                    math.sqrt((point[0] - line_point[0])**2 + (point[1] - line_point[1])**2)
                    for line_point in current_line
                )

                if min_dist_to_line <= max_distance_threshold:
                    current_line.append(remaining_points.pop(i))
                    added_point = True
                    break

        lines.append(current_line)

    return lines

def sort_single_line(line_points):
    """
    对单条线的点进行排序
    :param line_points: 单条线的点列表
    :return: 排序后的点列表
    """
    if len(line_points) <= 1:
        return line_points

    # 找到两个端点（邻近点数量为1的点）
    endpoints = []
    for point in line_points:
        distances = [math.sqrt((p[0]-point[0])**2 + (p[1]-point[1])**2) for p in line_points if p != point]
        if not distances:
            continue
        min_dist = min(distances)
        neighbors = [p for p in line_points if math.sqrt((p[0]-point[0])**2 + (p[1]-point[1])**2) <= 1.5 * min_dist and p != point]
        if len(neighbors) <= 1:
            endpoints.append(point)

    # 如果没有找到端点（闭合线条），选择最左上角的点作为起始点
    if len(endpoints) < 2:
        start_point = min(line_points, key=lambda p: (p[1], p[0]))  # 按y坐标，然后x坐标排序
    else:
        # 选择最左上角的端点作为起始点
        start_point = min(endpoints, key=lambda p: (p[1], p[0]))

    sorted_points = [start_point]
    remaining_points = line_points.copy()
    remaining_points.remove(start_point)

    while remaining_points:
        last_point = sorted_points[-1]
        # 找到离last_point最近的点
        nearest_point = min(remaining_points,
                          key=lambda p: math.sqrt((p[0]-last_point[0])**2 + (p[1]-last_point[1])**2))
        sorted_points.append(nearest_point)
        remaining_points.remove(nearest_point)

    return sorted_points

def create_boxes_from_points(valid_results, window_size=3, max_height_threshold=50):
    """
    从点创建矩形框，并过滤掉height较大的框以及框内的点

    Args:
        valid_results: 包含(det_result, point_coords)元组的列表
        window_size: 窗口大小，默认为3
        max_height_threshold: 最大height阈值，超过此值的框将被过滤掉，默认为50

    Returns:
        tuple: (filtered_boxes, filtered_valid_results)
            - filtered_boxes: 过滤后的矩形框列表
            - filtered_valid_results: 过滤后的有效结果列表（删除了height较大框内的点）
    """
    boxes = []
    filtered_valid_results = []
    valid_point_indices = set()  # 记录保留的点的索引

    # 遍历点，每次跳过window_size个点
    for i in range(0, len(valid_results) - window_size + 1, window_size):
        # 获取当前窗口的点
        window_points = valid_results[i:i+window_size]

        min_x = min(p[1][0] for p in window_points)
        max_x = max(p[1][0] for p in window_points)

        # 确定矩形框的y坐标（取最小y作为底部，最大y+固定高度作为顶部）
        min_y = min(p[1][1] for p in window_points)
        max_y = max(p[1][1] for p in window_points)
        height = max_y - min_y
        width = max_x - min_x
        print(f"height: {height}, threshold: {max_height_threshold}")
        print(f"width: {width}")

        # 只保留height小于等于阈值的框
        if height <= max_height_threshold:
            # 创建矩形框 (x1, y1, x2, y2, confidence)
            box = (min_x, min_y, max_x, max_y, window_points[0][0].confidence)
            boxes.append(box)

            # 记录这个窗口内的点索引，这些点将被保留
            for j in range(i, i + window_size):
                if j < len(valid_results):
                    valid_point_indices.add(j)
        elif height > max_height_threshold and width > 2* max_height_threshold:
            print(f"过滤掉height={height},width={width}的框，超过阈值{max_height_threshold}")

    # 根据保留的点索引构建过滤后的valid_results
    filtered_valid_results = [valid_results[i] for i in range(len(valid_results)) if i in valid_point_indices]

    print(f"原始点数: {len(valid_results)}, 过滤后点数: {len(filtered_valid_results)}")
    print(f"原始框数: {(len(valid_results) - window_size + 1) // window_size if len(valid_results) >= window_size else 0}, 过滤后框数: {len(boxes)}")

    return boxes, filtered_valid_results

# def create_boxes_from_points(valid_results, window_size=3, max_height_threshold=50):
#     """
#     从点创建矩形框，当height超过阈值时扩大window_size至2倍重新计算

#     Args:
#         valid_results: 包含(det_result, point_coords)元组的列表
#         window_size: 窗口大小，默认为3
#         max_height_threshold: 最大height阈值，超过此值时扩大窗口，默认为50

#     Returns:
#         list: 矩形框列表
#     """
#     boxes = []
#     i = 0

#     while i < len(valid_results):
#         current_window_size = window_size

#         # 确保不超出数组边界
#         if i + current_window_size > len(valid_results):
#             current_window_size = len(valid_results) - i

#         # 获取当前窗口的点
#         window_points = valid_results[i:i+current_window_size]

#         if len(window_points) == 0:
#             break

#         min_x = min(p[1][0] for p in window_points)
#         max_x = max(p[1][0] for p in window_points)

#         # 确定矩形框的y坐标
#         min_y = min(p[1][1] for p in window_points)
#         max_y = max(p[1][1] for p in window_points)
#         height = max_y - min_y

#         print(f"窗口起始索引{i}, 窗口大小: {current_window_size}, height: {height}, threshold: {max_height_threshold}")

#         # 如果height超过阈值，尝试扩大窗口至2倍
#         if height > max_height_threshold:
#             expanded_window_size = current_window_size * 2

#             # 确保扩大后的窗口不超出数组边界
#             if i + expanded_window_size <= len(valid_results):
#                 print(f"height={height}超过阈值{max_height_threshold}，扩大窗口至{expanded_window_size}")

#                 # 重新计算扩大窗口的点
#                 window_points = valid_results[i:i+expanded_window_size]

#                 min_x = min(p[1][0] for p in window_points)
#                 max_x = max(p[1][0] for p in window_points)
#                 min_y = min(p[1][1] for p in window_points)
#                 max_y = max(p[1][1] for p in window_points)
#                 height = max_y - min_y

#                 print(f"扩大后窗口大小: {expanded_window_size}, 新height: {height}")
#                 current_window_size = expanded_window_size
#             else:
#                 print(f"无法扩大窗口（会超出边界），保持原窗口大小{current_window_size}")

#         # 创建矩形框 (x1, y1, x2, y2, confidence)
#         box = (min_x, min_y, max_x, max_y, window_points[0][0].confidence)
#         boxes.append(box)

#         # 移动到下一个窗口
#         i += current_window_size

#     print(f"总点数: {len(valid_results)}, 生成框数: {len(boxes)}")

#     return boxes


def compute_3d_distances(valid_results, camera_converter):
    camera_coords = []
    for det_result, point_coords in valid_results:
        d = det_result.physical_distance
        if d <= 0:
            camera_coords.append(None)
        else:
            cam_point = camera_converter.pixel_to_camera_coordinate(point_coords[0], point_coords[1], d)
            camera_coords.append(cam_point)

    # distances = []
    # for i in range(1, len(camera_coords)):
    #     p1 = camera_coords[i - 1]
    #     p2 = camera_coords[i]
    #     if p1 is None or p2 is None:
    #         distances.append(None)
    #     else:
    #         dx = p1.x - p2.x
    #         dy = p1.y - p2.y
    #         dz = p1.z - p2.z
    #         dist = np.sqrt(dx**2 + dy**2 + dz**2)
    #         distances.append(dist)
    # return distances
    return camera_coords

def is_bulge_by_y(z_mean, y_val):
    """
    根据 z 均值所在区间，判断相机坐标 y 是否超过隆起阈值
    """
    if z_mean <= 25:          # 0–20 cm
        return y_val > 8
    elif z_mean <= 30:        # 20–30 cm
        return y_val > 8.4
    elif z_mean <= 40:        # 30–40 cm
        return y_val > 8.7
    elif z_mean <= 50:        # 40–50 cm
        return y_val > 8.9
    else:                       # >50 cm，可按需拓展
        return y_val > 9.3


def filter_bulge_points(coords: List[CameraCoordinate]):
    # 提取 z 值用于计算均值（剔除 z<=0 的点）
    z_vals = np.array([p.z for p in coords if p.z > 0])
    if len(z_vals) == 0:
        return [], list(range(len(coords)))  # 全部无效
    
    z_mean = z_vals.mean()
    print(f"z_mean: {z_mean}")
    z_min = min(z_vals)
    print(f"z_min: {z_min}")
    z_midean = np.median(z_vals)
    print(f"z_midean: {z_midean}")
    bulge_indices = []
    kept_indices = []

    for idx, p in enumerate(coords):
        if p.z <= 0:
            continue
        if is_bulge_by_y(z_mean, p.y):
            bulge_indices.append(idx)
        else:
            kept_indices.append(idx)

    return kept_indices, bulge_indices

from sklearn.ensemble import IsolationForest

def detect_bulges_isolation_forest(distances):
    X = np.array(distances).reshape(-1, 1)
    clf = IsolationForest(contamination='auto', random_state=60)
    # clf = IsolationForest(contamination=0.25, random_state=60)
    outliers = clf.fit_predict(X)
    bulge_indices = np.where(outliers == -1)[0]
    return [(i, distances[i]) for i in bulge_indices]

def find_outlier_indices(distance_list):
    """
    基于滑动窗口和峰值检测方法找出异常点的索引

    参数:
        distance_list (list of str or float): 原始距离序列
        window_size (int): 滑动窗口大小（默认 10）
        threshold_ratio (float): 阈值比例（默认 1.3）
        prominence (float): 峰值突出度（默认 10）

    返回:
        outlier_indices (list of int): 异常值的索引位置
    """
    data = [float(x) for x in distance_list]
    
    bulges = detect_bulges_isolation_forest(data)


    return bulges

def main():
    args = parse_args()
    cap = cv2.VideoCapture(args.camera_id)
    cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
    cap.set(cv2.CAP_PROP_FPS, 30)
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, args.frame_size[0])
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, args.frame_size[1])
    cv2.namedWindow("监控视角 - 检测结果", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("监控视角 - 检测结果", 1280, 720)

    calibration_mapx = "pixel_to_physical_py/config/mapx"
    calibration_mapy = "pixel_to_physical_py/config/mapy"
    physical_processor = _initialize_physical_processor()
    camera_converter = _initialize_camera_converter()

    model = load_model(args.checkpoint, args.device)
    line_model = load_model(args.line_checkpoint, args.device)
    main_pp = build_preprocessing_pipeline()
    test_cfg = EasyDict(
        max_per_img=300,
        multi_label=True,
        nms=dict(iou_threshold=0.7, type='nms'),
        nms_pre=30000,
        score_thr=0.001)

    frame_count, start_time = 0, time.time()
    print("开始实时检测，按ESC键退出...")

    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        original_shape = rgb_frame.shape
        rgb_frame = _preprocess_image(rgb_frame, calibration_mapx, calibration_mapy)
        
        

        main_data, main_samples = preprocess_image(rgb_frame, main_pp['pipeline'], main_pp['preprocessor'], args.device)
        main_result = model(main_data)
        main_results = process_detection_results(main_result, args.device, test_cfg, original_shape,
                                                 main_samples.get('scale', 1), main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32)))

        line_data, _ = preprocess_image(rgb_frame, main_pp['pipeline'], main_pp['preprocessor'], args.device)
        line_result = line_model(line_data)
        line_preds, line_scores = torch.split(line_result[0], [2, 1], dim=1)
        _, line_points, _ = get_single_pred(line_scores, line_preds, (640, 640))

        scale_factor = main_samples.get('scale', 1)
        pad_param = main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32))
        transformed_points = [transform_point(p, scale_factor, pad_param) for p in line_points if p[2] > 0.5]

        # 先绘制原始检测点（红色小圆点）
        for pt in transformed_points:
            cv2.circle(rgb_frame, (int(pt[0]), int(pt[1])), 5, (0, 0, 255), -1)
        
        transformed_points = sort_points_by_line(transformed_points)

        distance_table_path = "pixel_to_physical_py/config/distance_table"
        detection_results = _convert_points_to_detection_results(transformed_points)
        valid_results = []

        for i, det_result in enumerate(detection_results):
            physical_processor.process_detection_result(det_result, distance_table_path)
            if det_result.physical_distance > 0:
                valid_results.append((det_result, transformed_points[i]))

        # 对valid_results中的transformed_points进行排序
        if valid_results:
            # 提取所有有效的transformed_points
            valid_transformed_points = [point_coords for _, point_coords in valid_results]
            # 对这些点进行排序
            sorted_valid_points = sort_points_by_line(valid_transformed_points)
            # 重新构建valid_results，保持det_result和排序后的点的对应关系
            valid_results = [(valid_results[i][0], sorted_valid_points[i]) for i in range(len(valid_results))]

        # if len(sorted_valid_points) > 10:
            # print(f"排序后的像素坐标(x,y): {[f'({p[0]},{p[1]})' for p in transformed_points]}")
            # 绘制填充的圆圈作为背景
            # for i,pt in enumerate(sorted_valid_points):
            #     point_x, point_y = (int(pt[0]), int(pt[1]))
            #    # print(point_x, point_y)
            #     cv2.circle(rgb_frame, (point_x, point_y), 6, (0, 255, 0), -1)
            #     # 在圆圈内绘制距离数字（黑色）
            #     # 计算文字位置（居中）
            #     text_size = cv2.getTextSize(f'{i}', cv2.FONT_HERSHEY_SIMPLEX, 0.2, 1)[0]
            #     text_x = point_x - text_size[0] // 2
            #     text_y = point_y + text_size[1] // 2
            #     cv2.putText(rgb_frame, f'{i}', (text_x, text_y),
            #                 cv2.FONT_HERSHEY_SIMPLEX, 0.2, (0, 0, 0), 1)

        camera_coord = compute_3d_distances(valid_results, camera_converter)
        # kept_indices, outlier_indices_camera = filter_bulge_points(camera_coord)
        # valid_results = [valid_results[i] for i in kept_indices]
        # print("valid_results:", valid_results)
        print("camera_y: ", [p.y for p in camera_coord if p is not None])
            # distances = [result[0].physical_distance for result in valid_results]
            # outlier_indices = find_outlier_indices(distances)
            # valid_results = [valid_results[i] for i in range(len(valid_results)) if i not in outlier_indices]
        
        # 生成矩形框，当height超过阈值时扩大window_size
        # boxes = create_boxes_from_points(valid_results, window_size=8, max_height_threshold=20)
        boxes, filtered_valid_results = create_boxes_from_points(valid_results, window_size=10, max_height_threshold=100)
        
        box_detection_results = _convert_boxes_to_detection_results(boxes)
        box_valid_results = []
        for box_det_result in box_detection_results:
            physical_processor.process_detection_result(box_det_result, distance_table_path)
            if box_det_result.physical_distance > 0:
                box_valid_results.append(box_det_result)
        valid_results = filtered_valid_results
        # 可视化距离值
        print(f"\n=== 可视化 {len(valid_results)} 个检测点的距离值 ===")
        for i, (det_result, point_coords) in enumerate(valid_results):
            point_x, point_y = int(point_coords[0]), int(point_coords[1])
            distance_value = det_result.physical_distance
            

            # 在圆圈内显示距离值
            point_x, point_y = int(point_coords[0]), int(point_coords[1])
            distance_value = int(det_result.physical_distance)  # 取整数，更简洁
            distance_text = f"{distance_value}"

            # 根据距离选择颜色
            if distance_value < 30:
                circle_color = (0, 255, 0)  # 绿色 - 近距离
            elif distance_value < 60:
                circle_color = (255, 255, 0)  # 黄色 - 中距离
            else:
                circle_color = (255, 0, 0)  # 红色 - 远距离

            border_color = (255, 255, 255)  # 白色边框

            # 圆圈和字体设置
            circle_radius = 8
            font_scale = 0.3

            # 绘制填充的圆圈作为背景
            cv2.circle(rgb_frame, (point_x, point_y), circle_radius, circle_color, -1)

            # 绘制圆圈边框（修正过的点用不同颜色边框）
            cv2.circle(rgb_frame, (point_x, point_y), circle_radius, border_color, 2)

            # 计算文字位置（居中）
            text_size = cv2.getTextSize(distance_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, 1)[0]
            text_x = point_x - text_size[0] // 2
            text_y = point_y + text_size[1] // 2

            # 在圆圈内绘制距离数字（黑色）
            cv2.putText(rgb_frame, distance_text, (text_x, text_y),
                        cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), 1)

            print(f"  点 {i+1}: 位置({point_x}, {point_y}), 距离={distance_value:.1f}cm")

        for box_det_result in box_valid_results:
            bbox = box_det_result.bbox
            cv2.rectangle(rgb_frame, (bbox.x1, bbox.y1), (bbox.x2, bbox.y2), (0, 255, 0), 2)
            text_size = cv2.getTextSize(f"{box_det_result.physical_distance:.1f}cm", cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
            text_x = bbox.x1
            text_y = bbox.y1 - 10
            cv2.putText(rgb_frame, f"{box_det_result.physical_distance:.1f}cm", (text_x, text_y),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
            print(f"  矩形框: ({bbox.x1}, {bbox.y1}) - ({bbox.x2}, {bbox.y2}), 距离={box_det_result.physical_distance:.1f}cm")


        # 在去畸变图像上绘制范围框
        x_min, x_max = (0, 1280)
        y_min, y_max = (420, 700)
        # 绘制矩形框 (红色，线宽2)
        cv2.rectangle(rgb_frame, (x_min, y_min), (x_max, y_max), (128, 255, 0), 2)
        display_img = cv2.cvtColor(rgb_frame, cv2.COLOR_RGB2BGR)
        cv2.imwrite(f"output/{frame_count}.png", display_img)
        frame_count += 1
        if args.fps_display and frame_count % 10 == 0:
            fps = frame_count / (time.time() - start_time)
            cv2.putText(display_img, f"FPS: {fps:.2f}", (20, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

        cv2.imshow("监控视角 - 检测结果", display_img)
        if cv2.waitKey(1) == 27:
            break

    cap.release()
    cv2.destroyAllWindows()
    print("程序已结束")

if __name__ == '__main__':
    main()

